
import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, ExternalLink, Eye } from 'lucide-react';

interface QAPair {
  question: string;
  answer: string;
}

interface RelatedPost {
  post_title: string;
  relevance_summary: string;
  qa_pairs: QAPair[];
  score: number;
  post_url: string;
}

interface ForYouWidgetProps {
  relatedPosts: RelatedPost[];
}

const ForYouWidget: React.FC<ForYouWidgetProps> = ({
  relatedPosts
}) => {
  // Create a unique key for this session based on post titles
  const sessionKey = `for-you-expanded-${relatedPosts.map(p => p.post_title).join('').slice(0, 50)}`;

  const [expandedPosts, setExpandedPosts] = useState<number[]>([]);
  const [expandedQAs, setExpandedQAs] = useState<string[]>([]);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Initialize expanded state on first load
  useEffect(() => {
    if (!hasInitialized && relatedPosts.length > 0) {
      // Try to load saved state from sessionStorage
      const savedState = sessionStorage.getItem(sessionKey);
      if (savedState) {
        try {
          const parsed = JSON.parse(savedState);
          setExpandedPosts(parsed.expandedPosts || []);
          setExpandedQAs(parsed.expandedQAs || []);
        } catch (error) {
          console.warn('Failed to parse saved state:', error);
          // Default to all posts closed if no saved state
          setExpandedPosts([]);
        }
      } else {
        // Default to all posts closed on first visit
        setExpandedPosts([]);
      }
      setHasInitialized(true);
    }
  }, [relatedPosts, sessionKey, hasInitialized]);

  // Save state to sessionStorage whenever it changes
  useEffect(() => {
    if (hasInitialized) {
      const stateToSave = {
        expandedPosts,
        expandedQAs
      };
      sessionStorage.setItem(sessionKey, JSON.stringify(stateToSave));
    }
  }, [expandedPosts, expandedQAs, sessionKey, hasInitialized]);

  const togglePost = (index: number) => {
    setExpandedPosts(prev => {
      if (prev.includes(index)) {
        // Remove from expanded posts
        const newExpanded = prev.filter(i => i !== index);
        // Also remove all QAs for this post when collapsing
        setExpandedQAs(prevQAs => prevQAs.filter(qa => !qa.startsWith(`${index}-`)));
        return newExpanded;
      } else {
        // Add to expanded posts
        return [...prev, index];
      }
    });
  };

  const toggleQA = (questionId: string) => {
    setExpandedQAs(prev => {
      if (prev.includes(questionId)) {
        // Remove from expanded QAs
        return prev.filter(qa => qa !== questionId);
      } else {
        // Add to expanded QAs
        return [...prev, questionId];
      }
    });
  };

  const handleOpenPost = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(url, '_blank');
  };

  return (
    <div className="space-y-8">
      {relatedPosts.map((post, index) => (
        <div
          key={index}
          className="bg-gradient-to-br from-white to-gray-50/30 border-2 border-gray-200/80 rounded-2xl overflow-hidden shadow-md transition-all duration-300 fade-in-up"
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          {/* Post Header - Always Visible */}
          <div className="p-4 sm:p-6">
            <div className="flex items-start gap-4 mb-4">
              <div className="flex-1">
                {/* Title with embedded link arrow - Only title is clickable */}
                <div
                  className="flex items-start gap-2 mb-3 group cursor-pointer hover:bg-gray-50/50 p-2 -m-2 rounded-lg transition-colors"
                  onClick={(e) => handleOpenPost(post.post_url, e)}
                >
                  <h4 className="font-bold text-gray-700 text-base sm:text-lg leading-tight line-clamp-2 group-hover:text-green-600 transition-colors">
                    {post.post_title}
                  </h4>
                  <ExternalLink
                    size={16}
                    className="text-gray-400 group-hover:text-green-500 transition-colors flex-shrink-0 mt-1"
                  />
                </div>

                {/* Expand Button - Left side under title */}
                <button
                  onClick={() => togglePost(index)}
                  className="flex items-center gap-2 px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors text-sm font-medium"
                >
                  <Eye size={14} />
                  <span className="hidden sm:inline">
                    {expandedPosts.includes(index) ? 'Collapse Details' : 'View Details'}
                  </span>
                  <span className="sm:hidden">
                    {expandedPosts.includes(index) ? 'Less' : 'More'}
                  </span>
                  {expandedPosts.includes(index) ? (
                    <ChevronUp size={16} />
                  ) : (
                    <ChevronDown size={16} />
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Expanded Content */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden ${
              expandedPosts.includes(index)
                ? 'max-h-[2000px] opacity-100'
                : 'max-h-0 opacity-0'
            }`}
          >
            <div className="border-t-2 border-gray-200/60">
              {/* Relevance Summary */}
              <div className="p-4 sm:p-6 bg-gradient-to-br from-gray-50/30 via-gray-50/20 to-gray-50/30 relative overflow-hidden">
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-green-200/10 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

                <h5 className="font-semibold text-gray-700 mb-4 flex items-center gap-3 relative z-10">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full shadow-sm">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                  <span className="text-base sm:text-lg">How this relates to the current post</span>
                </h5>
                <div className="bg-white/90 backdrop-blur-sm rounded-xl p-4 sm:p-5 border border-gray-100/80 shadow-sm relative z-10">
                  <p className="text-gray-700 leading-relaxed text-sm sm:text-base whitespace-pre-line">
                    {post.relevance_summary}
                  </p>
                </div>
              </div>

              {/* Q&A Section */}
              {post.qa_pairs && post.qa_pairs.length > 0 && (
                <div className="p-4 sm:p-6 bg-gradient-to-br from-gray-50/80 to-gray-50/40">
                  <h5 className="font-bold text-gray-700 mb-6 flex items-center gap-3">
                    <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg">
                      <div className="w-3 h-3 bg-white rounded-full"></div>
                    </div>
                    <span className="text-lg sm:text-xl">Quick Q&A ({post.qa_pairs.length})</span>
                  </h5>

                  <div className="space-y-3">
                    {post.qa_pairs.map((qa, qaIndex) => {
                      const questionId = `${index}-${qaIndex}`;
                      const isExpanded = expandedQAs.includes(questionId);

                      return (
                        <div
                          key={qaIndex}
                          className="bg-white/90 backdrop-blur-sm border border-gray-200/80 rounded-2xl overflow-hidden hover:border-green-500/60 hover:shadow-lg transition-all duration-300 individual-qa-item"
                        >
                          <button
                            onClick={() => toggleQA(questionId)}
                            className="w-full p-5 sm:p-6 text-left hover:bg-gradient-to-r hover:from-green-50/60 hover:to-gray-50/40 transition-all duration-300 group"
                          >
                            <div className="flex justify-between items-start gap-4">
                              <div className="flex items-start gap-4 flex-1">
                                <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-br from-gray-100 to-gray-100 rounded-xl flex items-center justify-center mt-0.5 group-hover:from-green-100 group-hover:to-green-100 transition-all duration-300">
                                  <div className="w-3 h-3 bg-gradient-to-br from-green-500 to-green-600 rounded-full"></div>
                                </div>
                                <span className="text-base sm:text-lg font-semibold text-gray-700 leading-relaxed group-hover:text-green-700 transition-colors duration-300">
                                  {qa.question}
                                </span>
                              </div>
                              <div className="flex-shrink-0 mt-1">
                                {isExpanded ? (
                                  <ChevronUp size={20} className="text-gray-500 group-hover:text-green-600 transition-colors duration-300" />
                                ) : (
                                  <ChevronDown size={20} className="text-gray-500 group-hover:text-green-600 transition-colors duration-300" />
                                )}
                              </div>
                            </div>
                          </button>

                          <div
                            className={`transition-all duration-500 ease-in-out overflow-hidden ${
                              isExpanded
                                ? 'max-h-96 opacity-100'
                                : 'max-h-0 opacity-0'
                            }`}
                          >
                            <div className="px-5 sm:px-6 pb-5 sm:pb-6 border-t border-gray-200/60 bg-gradient-to-br from-green-50/30 to-gray-50/20">
                              <div className="pt-5 text-base sm:text-lg text-gray-700 leading-relaxed whitespace-pre-line pl-12 font-medium">
                                {qa.answer}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}

      {relatedPosts.length === 0 && (
        <div className="text-center py-16 px-6">
          <div className="relative mb-6">
            <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto shadow-sm">
              <Eye size={32} className="text-gray-400" />
            </div>
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-green-200/40 to-green-200/30 rounded-full"></div>
          </div>
          <h3 className="text-gray-600 text-xl font-semibold mb-2">No related posts found</h3>
          <p className="text-gray-400 text-base max-w-sm mx-auto leading-relaxed">
            We're always adding new content. Check back later for personalized recommendations!
          </p>
        </div>
      )}
    </div>
  );
};

export default ForYouWidget;
